package com.geeksec.alarm.notification.function;

import java.util.List;
import java.util.Map;

import org.apache.flink.api.common.state.BroadcastState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.state.ReadOnlyBroadcastState;
import org.apache.flink.streaming.api.functions.co.BroadcastProcessFunction;
import org.apache.flink.util.Collector;

import com.geeksec.alarm.notification.model.Alarm;
import com.geeksec.alarm.notification.model.NotificationChannel;
import com.geeksec.alarm.notification.model.NotificationResult;
import com.geeksec.alarm.notification.model.NotificationSubscription;
import com.geeksec.alarm.notification.model.SubscriptionRule;

import lombok.extern.slf4j.Slf4j;

/**
 * 通知处理函数
 * 使用专门的流处理模型
 */
@Slf4j
public class NotificationFunction extends BroadcastProcessFunction<Alarm, NotificationSubscription, NotificationResult> {
    
    public static final MapStateDescriptor<String, NotificationSubscription> SUBSCRIPTION_STATE_DESCRIPTOR =
            new MapStateDescriptor<>("subscription-state", String.class, NotificationSubscription.class);
    
    @Override
    public void processElement(Alarm alarm, ReadOnlyContext ctx, Collector<NotificationResult> out) throws Exception {
        // 获取所有订阅配置
        ReadOnlyBroadcastState<String, NotificationSubscription> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        // 遍历匹配订阅
        for (Map.Entry<String, NotificationSubscription> entry : subscriptionState.immutableEntries()) {
            NotificationSubscription subscription = entry.getValue();
            
            if (matchesSubscription(alarm, subscription)) {
                // 发送通知
                sendNotifications(alarm, subscription, out);
            }
        }
    }
    
    private boolean matchesSubscription(Alarm alarm, NotificationSubscription subscription) {
        if (!Boolean.TRUE.equals(subscription.getEnabled()) || 
            subscription.getRules() == null || subscription.getRules().isEmpty()) {
            return true;
        }
        
        return subscription.getRules().stream()
                .allMatch(rule -> matchesRule(alarm, rule));
    }
    
    @Override
    public void processBroadcastElement(NotificationSubscription subscription, Context ctx, Collector<NotificationResult> out) throws Exception {
        // 获取广播状态
        BroadcastState<String, NotificationSubscription> subscriptionState = 
                ctx.getBroadcastState(SUBSCRIPTION_STATE_DESCRIPTOR);
        
        if (subscription != null && subscription.getSubscriptionId() != null) {
            if (Boolean.TRUE.equals(subscription.getEnabled())) {
                // 添加或更新订阅配置
                subscriptionState.put(subscription.getSubscriptionId(), subscription);
                log.info("更新订阅配置: subscriptionId={}, enabled={}", 
                        subscription.getSubscriptionId(), subscription.getEnabled());
            } else {
                // 删除已禁用的订阅配置
                subscriptionState.remove(subscription.getSubscriptionId());
                log.info("删除已禁用的订阅配置: subscriptionId={}", subscription.getSubscriptionId());
            }
        } else {
            log.warn("收到无效的订阅配置，跳过处理: {}", subscription);
        }
    }
    
    private boolean matchesRule(Alarm alarm, SubscriptionRule rule) {
        // 简化的规则匹配逻辑
        String fieldValue = getFieldValue(alarm, rule.getFieldName());
        String expectedValue = rule.getExpectedValue();
        
        if (fieldValue == null || expectedValue == null) {
            return false;
        }
        
        boolean ignoreCase = Boolean.TRUE.equals(rule.getIgnoreCase());
        if (ignoreCase) {
            fieldValue = fieldValue.toLowerCase();
            expectedValue = expectedValue.toLowerCase();
        }
        
        return switch (rule.getOperator()) {
            case "EQUALS" -> fieldValue.equals(expectedValue);
            case "CONTAINS" -> fieldValue.contains(expectedValue);
            default -> false;
        };
    }
    
    private String getFieldValue(Alarm alarm, String fieldName) {
        // 根据字段名获取告警对象的字段值
        return switch (fieldName) {
            case "alarmType" -> alarm.getAlarmType();
            case "alarmLevel" -> alarm.getAlarmLevel() != null ? alarm.getAlarmLevel().toString() : null;
            case "sourceModule" -> alarm.getSourceModule();
            case "alarmName" -> alarm.getAlarmName();
            case "description" -> alarm.getDescription();
            case "threatType" -> alarm.getThreatType();
            case "srcIp" -> alarm.getSrcIp();
            case "dstIp" -> alarm.getDstIp();
            case "protocol" -> alarm.getProtocol();
            default -> null;
        };
    }
    
    private void sendNotifications(Alarm alarm, NotificationSubscription subscription, Collector<NotificationResult> out) {
        try {
            List<NotificationChannel> channels = subscription.getChannels();
            if (channels == null || channels.isEmpty()) {
                log.warn("订阅 {} 没有配置通知渠道", subscription.getSubscriptionId());
                return;
            }
            
            for (NotificationChannel channel : channels) {
                if (Boolean.TRUE.equals(channel.getEnabled())) {
                    // 创建通知结果
                    NotificationResult result = NotificationResult.builder()
                            .alarmId(alarm.getAlarmId())
                            .subscriptionId(subscription.getSubscriptionId())
                            .channelType(channel.getChannelType())
                            .recipient(channel.getAddress())
                            .subject("告警通知 - " + alarm.getAlarmName())
                            .content(buildNotificationContent(alarm, subscription))
                            .sendTime(java.time.LocalDateTime.now())
                            .sendStatus("PENDING")
                            .build();
                    
                    out.collect(result);
                    log.debug("发送通知: alarmId={}, subscriptionId={}, channelType={}", 
                            alarm.getAlarmId(), subscription.getSubscriptionId(), channel.getChannelType());
                }
            }
        } catch (Exception e) {
            log.error("发送通知失败: alarmId={}, subscriptionId={}, error={}", 
                    alarm.getAlarmId(), subscription.getSubscriptionId(), e.getMessage(), e);
        }
    }
    
    private String buildNotificationContent(Alarm alarm, NotificationSubscription subscription) {
        // 构建通知内容
        return String.format("告警通知\n" +
                "告警ID: %s\n" +
                "告警类型: %s\n" +
                "告警名称: %s\n" +
                "告警级别: %s\n" +
                "威胁类型: %s\n" +
                "告警描述: %s\n" +
                "事件时间: %s\n" +
                "处理时间: %s\n" +
                "订阅ID: %s",
                alarm.getAlarmId(),
                alarm.getAlarmType(),
                alarm.getAlarmName(),
                alarm.getAlarmLevel(),
                alarm.getThreatType(),
                alarm.getDescription(),
                alarm.getEventTimestamp(),
                alarm.getProcessedTimestamp(),
                subscription.getSubscriptionId());
    }
}
