# 告警通知模式配置指南

## 概述

告警通知作业支持两种运行模式，可以通过配置参数控制是否记录通知发送结果。

## 运行模式

### 1. 标准模式（记录通知结果）

**启用条件：** `performanceLogging=true`

**特点：**
- 记录每次通知发送的详细结果
- 生成 `NotificationResult` 对象
- 支持通知结果的后续处理（如写入数据库、发送到监控系统等）
- 提供完整的通知发送审计日志

**使用场景：**
- 需要监控通知发送成功率
- 需要审计通知发送记录
- 需要分析通知发送性能
- 调试和故障排查

**配置示例：**
```bash
# 启用标准模式
--alarm.notification.monitoring.enabled=true
--alarm.notification.performance.logging=true
```

### 2. 直接模式（不记录通知结果）

**启用条件：** `performanceLogging=false`

**特点：**
- 直接发送通知，不生成结果记录
- 更低的内存占用和处理延迟
- 简化的数据流处理
- 仅在日志中记录发送成功/失败信息

**使用场景：**
- 高吞吐量场景，优先考虑性能
- 不需要详细的通知发送记录
- 资源受限的环境
- 简化的部署和运维

**配置示例：**
```bash
# 启用直接模式
--alarm.notification.monitoring.enabled=false
--alarm.notification.performance.logging=false
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `alarm.notification.performance.logging` | `false` | 控制是否记录通知结果 |
| `alarm.notification.monitoring.enabled` | `true` | 控制是否启用监控功能 |

## 性能对比

### 标准模式
- **内存使用：** 较高（需要存储 NotificationResult 对象）
- **处理延迟：** 较高（需要生成和处理结果对象）
- **功能完整性：** 完整的审计和监控功能

### 直接模式
- **内存使用：** 较低（不生成结果对象）
- **处理延迟：** 较低（直接发送通知）
- **功能完整性：** 基本的通知发送功能

## 代码实现差异

### 标准模式流程
```
告警数据 → 订阅匹配 → 生成NotificationResult → 发送通知 → 记录结果
```

### 直接模式流程
```
告警数据 → 订阅匹配 → 直接发送通知 → 记录日志
```

## 切换模式

### 运行时切换
可以通过重启作业并修改配置参数来切换模式：

```bash
# 从标准模式切换到直接模式
flink stop <job-id>
flink run -p 4 alarm-notification.jar --alarm.notification.performance.logging=false

# 从直接模式切换到标准模式
flink stop <job-id>
flink run -p 4 alarm-notification.jar --alarm.notification.performance.logging=true
```

### 配置文件切换
修改 `application.yml` 或环境变量：

```yaml
alarm:
  notification:
    performance:
      logging: false  # 设置为 false 启用直接模式
    monitoring:
      enabled: false  # 可选：同时禁用监控
```

## 监控和日志

### 标准模式日志示例
```
2024-07-22 14:30:15 INFO  通知发送结果: 订阅=sub-001, 告警=alarm-123, 渠道=EMAIL, 状态=SUCCESS, 接收者=<EMAIL>
```

### 直接模式日志示例
```
2024-07-22 14:30:15 DEBUG 通知发送成功: alarmId=alarm-123, subscriptionId=sub-001, channelType=EMAIL
```

## 建议

1. **生产环境：** 根据实际需求选择模式
   - 如果需要完整的审计功能，使用标准模式
   - 如果优先考虑性能，使用直接模式

2. **开发环境：** 建议使用标准模式，便于调试和测试

3. **监控：** 无论使用哪种模式，都建议启用基本的日志监控

4. **资源规划：** 直接模式可以减少约 20-30% 的内存使用和处理延迟

## 故障排查

### 标准模式故障排查
- 检查 NotificationResult 的状态字段
- 查看详细的错误信息和重试记录
- 分析通知发送的成功率统计

### 直接模式故障排查
- 主要依赖日志信息
- 检查 NotificationSender 的异步回调日志
- 监控通知发送的异常日志
