# SourceFunction 迁移指南

## 概述

本文档说明如何将废弃的 `SourceFunction` 接口迁移到新的 `Source` 接口，以适配 Flink 1.20.1。

## 迁移前后对比

### 迁移前 (SourceFunction)

```java
@Slf4j
public class SubscriptionConfigSource implements SourceFunction<NotificationSubscription> {
    
    private final AlarmServiceClient alarmServiceClient;
    private volatile boolean running = true;
    
    @Override
    public void run(SourceFunction.SourceContext<NotificationSubscription> ctx) throws Exception {
        // 直接在run方法中处理数据读取和发送
        List<NotificationSubscription> subscriptions = alarmServiceClient.getAllActiveSubscriptions();
        for (NotificationSubscription subscription : subscriptions) {
            if (!running) break;
            ctx.collect(subscription);
        }
    }
    
    @Override
    public void cancel() {
        running = false;
    }
}

// 使用方式
DataStream<NotificationSubscription> stream = env
    .addSource(new SubscriptionConfigSource(client))
    .name("subscription-source");
```

### 迁移后 (Source 接口)

```java
@Slf4j
public class SubscriptionConfigSource implements Source<NotificationSubscription, SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> {
    
    private final AlarmServiceClient alarmServiceClient;
    
    @Override
    public Boundedness getBoundedness() {
        return Boundedness.BOUNDED; // 有界数据源
    }
    
    @Override
    public SourceReader<NotificationSubscription, SubscriptionConfigSplit> createReader(SourceReaderContext readerContext) {
        return new SubscriptionConfigSourceReader(alarmServiceClient, readerContext);
    }
    
    @Override
    public SplitEnumerator<SubscriptionConfigSplit, SubscriptionConfigEnumeratorState> createEnumerator(
            SplitEnumeratorContext<SubscriptionConfigSplit> enumContext) {
        return new SubscriptionConfigSplitEnumerator(enumContext);
    }
    
    // ... 其他必需方法
}

// 使用方式
SubscriptionConfigSource source = SubscriptionConfigSource.createDefault(client);
DataStream<NotificationSubscription> stream = env
    .fromSource(source, WatermarkStrategy.noWatermarks(), "subscription-source")
    .setParallelism(1);
```

## 新架构组件

### 1. SubscriptionConfigSplit
- 表示数据源的一个分片
- 实现 `SourceSplit` 接口
- 包含分片ID和完成状态

### 2. SubscriptionConfigEnumeratorState  
- 枚举器的检查点状态
- 用于故障恢复
- 包含已分配的分片信息

### 3. SubscriptionConfigSourceReader
- 实际的数据读取器
- 实现 `SourceReader` 接口
- 负责从外部系统读取数据并输出

### 4. SubscriptionConfigSplitEnumerator
- 分片枚举器
- 管理分片的分配和生命周期
- 处理故障恢复

### 5. 序列化器
- `SubscriptionConfigSplitSerializer`: 分片序列化
- `SubscriptionConfigEnumeratorStateSerializer`: 状态序列化

## 主要优势

1. **更好的容错性**: 支持检查点和故障恢复
2. **更好的并行性**: 支持分片并行处理
3. **更好的监控**: 提供更详细的指标和监控
4. **向前兼容**: 符合 Flink 未来版本的发展方向

## 注意事项

1. 新的 Source 接口相比 SourceFunction 更复杂，但提供了更强的功能
2. 对于简单的一次性数据读取场景，可以使用 `Boundedness.BOUNDED`
3. 需要正确实现序列化器以支持检查点功能
4. 分片枚举器需要正确处理分片分配逻辑

## 测试验证

编译成功后，可以通过以下方式验证迁移是否正确：

```bash
# 编译项目
mvn compile

# 运行测试
mvn test

# 打包
mvn package
```

## 相关文档

- [Flink Source 接口官方文档](https://nightlies.apache.org/flink/flink-docs-release-1.20/docs/dev/datastream/sources/)
- [Flink 1.20.1 发布说明](https://flink.apache.org/news/2024/12/19/release-1.20.1.html)
